package com.autolink.sbjk.common.constant;

/**
 * 相机相关常量定义
 * 车载监控系统专用配置
 */
public class CameraConstants {
    
    // 相机ID定义
    public static final String CAMERA_FRONT = "26";  // 前视
    public static final String CAMERA_BACK = "30";   // 后视
    public static final String CAMERA_LEFT = "34";   // 左视
    public static final String CAMERA_RIGHT = "38";  // 右视

    // 相机名称映射
    public static final String[] CAMERA_IDS = {
        CAMERA_FRONT, CAMERA_BACK, CAMERA_LEFT, CAMERA_RIGHT
    };

    public static final String[] CAMERA_NAMES = {
        "前视", "后视", "左视", "右视"
    };
    
    // 录制参数
    public static final int DEFAULT_WIDTH = 1280;
    public static final int DEFAULT_HEIGHT = 800;
    public static final int DEFAULT_BITRATE = 1000000; // 1Mbps
    public static final int DEFAULT_FRAME_RATE = 15;
    public static final int DEFAULT_I_FRAME_INTERVAL = 2;

    // 编码器比特率配置
    public static final int DEFAULT_BIT_RATE = 1000000;      // 1Mbps - 默认比特率
    public static final int HIGH_QUALITY_BIT_RATE = 2000000; // 2Mbps - 高质量比特率
    public static final int STANDARD_BIT_RATE = 1500000;     // 1.5Mbps - 标准比特率
    
    // 分段录制配置
    public static final int DEFAULT_SEGMENT_DURATION_MINUTES = 10;
    public static final long SEGMENT_CHECK_INTERVAL_MS = 5000; // 5秒检查一次
    
    // 缓冲区配置
    public static final int MAX_IMAGES = 4;
    public static final int FRAME_SKIP_THRESHOLD = 2;
    public static final int BUFFER_SIZE = 8 * 1024 * 1024; // 8MB
    
    // 相机状态
    public enum CameraStatus {
        IDLE,           // 空闲
        INITIALIZING,   // 初始化中
        RECORDING,      // 录制中
        PAUSED,         // 暂停
        ERROR,          // 错误
        STOPPED         // 已停止
    }
    
    // 编码格式
    public static final String CODEC_H264 = "H.264";
    public static final String CODEC_H265 = "H.265";
    public static final String DEFAULT_CODEC = CODEC_H264;
    
    // 文件格式
    public static final String FILE_EXTENSION_MP4 = ".mp4";
    public static final String FILE_EXTENSION_MKV = ".mkv";
    
    // 权限相关
    public static final String[] REQUIRED_PERMISSIONS = {
        android.Manifest.permission.CAMERA,
        android.Manifest.permission.RECORD_AUDIO,
        android.Manifest.permission.WRITE_EXTERNAL_STORAGE
    };
    
    // 错误码定义
    public static final int ERROR_CAMERA_ACCESS = 1001;
    public static final int ERROR_ENCODER_INIT = 1002;
    public static final int ERROR_STORAGE_FULL = 1003;
    public static final int ERROR_PERMISSION_DENIED = 1004;
    public static final int ERROR_HARDWARE_FAILURE = 1005;
    
    // 性能配置
    public static final int CAMERA_OPEN_TIMEOUT_MS = 2500;
    public static final int ENCODER_TIMEOUT_MS = 10000;
    public static final long MEMORY_CHECK_INTERVAL_MS = 30000; // 30秒检查一次内存
    
    // 存储路径
    public static final String DEFAULT_RECORD_PATH = "/storage/emulated/0/哨兵录像/";

    // 循环录制相关常量
    public static final double STORAGE_CLEANUP_THRESHOLD = 0.10; // 剩余空间不足10%时清理
    public static final double STORAGE_CLEANUP_TARGET = 0.02;    // 清理出2%的空间
    public static final long STORAGE_CHECK_INTERVAL_MS = 10 * 60 * 1000; // 10分钟检查一次
    
    private CameraConstants() {
        // 防止实例化
    }
    
    /**
     * 根据相机ID获取相机名称
     */
    public static String getCameraName(String cameraId) {
        for (int i = 0; i < CAMERA_IDS.length; i++) {
            if (CAMERA_IDS[i].equals(cameraId)) {
                return CAMERA_NAMES[i];
            }
        }
        return "摄像头" + cameraId;
    }
    
    /**
     * 检查是否为有效的相机ID
     */
    public static boolean isValidCameraId(String cameraId) {
        for (String id : CAMERA_IDS) {
            if (id.equals(cameraId)) {
                return true;
            }
        }
        return false;
    }
}
