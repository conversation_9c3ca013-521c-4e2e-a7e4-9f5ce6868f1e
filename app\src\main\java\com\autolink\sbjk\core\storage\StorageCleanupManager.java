package com.autolink.sbjk.core.storage;

import android.os.StatFs;
import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 存储清理管理器
 * 
 * 职责：
 * 1. 检查U盘存储空间
 * 2. 当剩余空间不足10%时清理出2%的空间
 * 3. 按文件创建时间删除最旧的文件
 * 
 * 设计原则：
 * - 简单直接，不过度设计
 * - 符合MVVM架构的Core层定位
 * - 线程安全的操作
 */
public class StorageCleanupManager {
    
    private static final String TAG = "StorageCleanupManager";
    
    private final String recordPath;
    
    public StorageCleanupManager(String recordPath) {
        this.recordPath = recordPath;
    }
    
    /**
     * 检查存储空间并在必要时进行清理
     * 
     * @return 清理结果信息
     */
    public StorageCleanupResult checkAndCleanup() {
        try {
            // 检查录制目录是否存在
            File recordDir = new File(recordPath);
            if (!recordDir.exists() || !recordDir.isDirectory()) {
                LogUtil.w(TAG, "录制目录不存在: " + recordPath);
                return new StorageCleanupResult(false, "录制目录不存在", 0, 0);
            }
            
            // 获取存储空间信息
            StatFs statFs = new StatFs(recordPath);
            long totalBytes = statFs.getTotalBytes();
            long availableBytes = statFs.getAvailableBytes();
            
            // 计算剩余空间百分比
            double availablePercent = (double) availableBytes / totalBytes;
            
            LogUtil.d(TAG, String.format("存储空间检查 - 总空间: %.2fGB, 剩余: %.2fGB (%.1f%%)", 
                totalBytes / (1024.0 * 1024.0 * 1024.0),
                availableBytes / (1024.0 * 1024.0 * 1024.0),
                availablePercent * 100));
            
            // 检查是否需要清理
            if (availablePercent > CameraConstants.STORAGE_CLEANUP_THRESHOLD) {
                LogUtil.d(TAG, "存储空间充足，无需清理");
                return new StorageCleanupResult(true, "存储空间充足", 0, 0);
            }
            
            // 需要清理，计算目标清理空间
            long targetCleanupBytes = (long) (totalBytes * CameraConstants.STORAGE_CLEANUP_TARGET);
            
            LogUtil.i(TAG, String.format("存储空间不足，开始清理。目标清理: %.2fGB", 
                targetCleanupBytes / (1024.0 * 1024.0 * 1024.0)));
            
            // 执行清理
            return performCleanup(recordDir, targetCleanupBytes);
            
        } catch (Exception e) {
            LogUtil.e(TAG, "存储空间检查失败", e);
            return new StorageCleanupResult(false, "检查失败: " + e.getMessage(), 0, 0);
        }
    }
    
    /**
     * 执行文件清理
     */
    private StorageCleanupResult performCleanup(File recordDir, long targetCleanupBytes) {
        try {
            // 获取所有录像文件
            List<File> videoFiles = getAllVideoFiles(recordDir);
            
            if (videoFiles.isEmpty()) {
                LogUtil.w(TAG, "没有找到可清理的录像文件");
                return new StorageCleanupResult(false, "没有可清理的文件", 0, 0);
            }
            
            // 按创建时间排序（最旧的在前）
            Collections.sort(videoFiles, (f1, f2) -> Long.compare(f1.lastModified(), f2.lastModified()));
            
            // 删除最旧的文件直到达到清理目标
            long cleanedBytes = 0;
            int cleanedCount = 0;
            
            for (File file : videoFiles) {
                if (cleanedBytes >= targetCleanupBytes) {
                    break;
                }
                
                long fileSize = file.length();
                if (file.delete()) {
                    cleanedBytes += fileSize;
                    cleanedCount++;
                    LogUtil.d(TAG, String.format("删除文件: %s (%.2fMB)", 
                        file.getName(), fileSize / (1024.0 * 1024.0)));
                } else {
                    LogUtil.w(TAG, "删除文件失败: " + file.getName());
                }
            }
            
            LogUtil.i(TAG, String.format("清理完成 - 删除文件: %d个, 释放空间: %.2fGB", 
                cleanedCount, cleanedBytes / (1024.0 * 1024.0 * 1024.0)));
            
            return new StorageCleanupResult(true, "清理成功", cleanedCount, cleanedBytes);
            
        } catch (Exception e) {
            LogUtil.e(TAG, "执行清理失败", e);
            return new StorageCleanupResult(false, "清理失败: " + e.getMessage(), 0, 0);
        }
    }
    
    /**
     * 获取所有录像文件
     */
    private List<File> getAllVideoFiles(File dir) {
        List<File> videoFiles = new ArrayList<>();
        
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile() && isVideoFile(file)) {
                    videoFiles.add(file);
                } else if (file.isDirectory()) {
                    // 递归搜索子目录
                    videoFiles.addAll(getAllVideoFiles(file));
                }
            }
        }
        
        return videoFiles;
    }
    
    /**
     * 判断是否为录像文件
     */
    private boolean isVideoFile(File file) {
        String name = file.getName().toLowerCase();
        return name.endsWith(".mp4") || name.endsWith(".avi") || name.endsWith(".mov");
    }
    
    /**
     * 获取存储空间信息
     */
    public StorageInfo getStorageInfo() {
        try {
            File recordDir = new File(recordPath);
            if (!recordDir.exists()) {
                return new StorageInfo(0, 0, 0);
            }
            
            StatFs statFs = new StatFs(recordPath);
            long totalBytes = statFs.getTotalBytes();
            long availableBytes = statFs.getAvailableBytes();
            long usedBytes = totalBytes - availableBytes;
            
            return new StorageInfo(totalBytes, usedBytes, availableBytes);
            
        } catch (Exception e) {
            LogUtil.e(TAG, "获取存储信息失败", e);
            return new StorageInfo(0, 0, 0);
        }
    }
    
    /**
     * 存储清理结果
     */
    public static class StorageCleanupResult {
        public final boolean success;
        public final String message;
        public final int cleanedFileCount;
        public final long cleanedBytes;
        
        public StorageCleanupResult(boolean success, String message, int cleanedFileCount, long cleanedBytes) {
            this.success = success;
            this.message = message;
            this.cleanedFileCount = cleanedFileCount;
            this.cleanedBytes = cleanedBytes;
        }
    }
    
    /**
     * 存储空间信息
     */
    public static class StorageInfo {
        public final long totalBytes;
        public final long usedBytes;
        public final long availableBytes;
        
        public StorageInfo(long totalBytes, long usedBytes, long availableBytes) {
            this.totalBytes = totalBytes;
            this.usedBytes = usedBytes;
            this.availableBytes = availableBytes;
        }
        
        public double getUsedPercent() {
            return totalBytes > 0 ? (double) usedBytes / totalBytes : 0;
        }
        
        public double getAvailablePercent() {
            return totalBytes > 0 ? (double) availableBytes / totalBytes : 0;
        }
    }
}
