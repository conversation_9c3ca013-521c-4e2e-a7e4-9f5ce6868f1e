package com.autolink.sbjk.core.camera;

import android.content.Context;
import android.view.Surface;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.encoder.CameraYuvEncoder;

/**
 * 相机控制器
 * 封装单个相机的操作逻辑
 */
public class CameraController {
    
    private static final String TAG = "CameraController";
    
    private final Context context;
    private final String cameraId;
    private CameraYuvEncoder encoder;
    private CameraConstants.CameraStatus status;
    private Surface previewSurface;
    private String currentOutputPath;

    // 分段完成回调
    private SegmentCompletionCallback segmentCompletionCallback;

    /**
     * 分段完成回调接口
     */
    public interface SegmentCompletionCallback {
        void onSegmentCompleted(String cameraId, String segmentPath);
    }
    
    public CameraController(Context context, String cameraId) {
        this.context = context;
        this.cameraId = cameraId;
        this.status = CameraConstants.CameraStatus.IDLE;
        
        LogUtil.d(TAG, "CameraController created for camera " + cameraId);
    }
    
    /**
     * 开始录制
     */
    public synchronized boolean startRecording(String outputPath) {
        try {
            if (status == CameraConstants.CameraStatus.RECORDING) {
                LogUtil.w(TAG, "Camera " + cameraId + " is already recording");
                return true;
            }
            
            if (status == CameraConstants.CameraStatus.ERROR) {
                LogUtil.e(TAG, "Camera " + cameraId + " is in error state");
                return false;
            }
            
            status = CameraConstants.CameraStatus.INITIALIZING;
            
            // 停止现有编码器
            if (encoder != null) {
                encoder.stopTest();
                encoder = null;
            }
            
            // 创建新的编码器
            encoder = new CameraYuvEncoder(context);

            // 设置分段完成回调
            if (segmentCompletionCallback != null) {
                encoder.setSegmentCompletionCallback((cameraId, segmentPath) -> {
                    segmentCompletionCallback.onSegmentCompleted(cameraId, segmentPath);
                });
            }

            // 设置预览Surface
            if (previewSurface != null) {
                encoder.setPreviewSurface(previewSurface);
            }

            // 设置录制路径
            encoder.setRecordPath(outputPath);

            // 启用分段录制
            encoder.setSegmentRecording(true, CameraConstants.DEFAULT_SEGMENT_DURATION_MINUTES);
            
            // 启动编码器
            encoder.startTest(cameraId);
            
            currentOutputPath = outputPath;
            status = CameraConstants.CameraStatus.RECORDING;
            
            LogUtil.i(TAG, "Recording started for camera " + cameraId);
            return true;
            
        } catch (Exception e) {
            status = CameraConstants.CameraStatus.ERROR;
            LogUtil.e(TAG, "Failed to start recording for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 停止录制
     */
    public synchronized boolean stopRecording() {
        try {
            if (status != CameraConstants.CameraStatus.RECORDING && 
                status != CameraConstants.CameraStatus.PAUSED) {
                LogUtil.w(TAG, "Camera " + cameraId + " is not recording");
                return true;
            }
            
            if (encoder != null) {
                encoder.stopTest();
                encoder = null;
            }
            
            status = CameraConstants.CameraStatus.IDLE;
            currentOutputPath = null;
            
            LogUtil.i(TAG, "Recording stopped for camera " + cameraId);
            return true;
            
        } catch (Exception e) {
            status = CameraConstants.CameraStatus.ERROR;
            LogUtil.e(TAG, "Failed to stop recording for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 暂停录制
     */
    public synchronized boolean pauseRecording() {
        try {
            if (status != CameraConstants.CameraStatus.RECORDING) {
                LogUtil.w(TAG, "Camera " + cameraId + " is not recording");
                return false;
            }
            
            // 注意：当前的CameraYuvEncoder不支持暂停，这里只是状态管理
            status = CameraConstants.CameraStatus.PAUSED;
            
            LogUtil.i(TAG, "Recording paused for camera " + cameraId);
            return true;
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to pause recording for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 恢复录制
     */
    public synchronized boolean resumeRecording() {
        try {
            if (status != CameraConstants.CameraStatus.PAUSED) {
                LogUtil.w(TAG, "Camera " + cameraId + " is not paused");
                return false;
            }
            
            status = CameraConstants.CameraStatus.RECORDING;
            
            LogUtil.i(TAG, "Recording resumed for camera " + cameraId);
            return true;
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to resume recording for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 设置预览Surface
     */
    public synchronized boolean setPreviewSurface(Surface surface) {
        try {
            this.previewSurface = surface;
            
            if (encoder != null) {
                encoder.setPreviewSurface(surface);
            }
            
            LogUtil.d(TAG, "Preview surface set for camera " + cameraId);
            return true;
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to set preview surface for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 更新预览Surface
     */
    public synchronized boolean updatePreviewSurface(Surface surface) {
        try {
            this.previewSurface = surface;

            if (encoder != null) {
                encoder.updatePreviewSurface(surface);
            }

            LogUtil.d(TAG, "Preview surface updated for camera " + cameraId);
            return true;

        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to update preview surface for camera " + cameraId, e);
            return false;
        }
    }

    /**
     * 暂停预览但保持录制
     */
    public synchronized boolean pausePreview() {
        try {
            if (encoder != null) {
                encoder.pausePreview();
            }

            LogUtil.d(TAG, "Preview paused for camera " + cameraId);
            return true;

        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to pause preview for camera " + cameraId, e);
            return false;
        }
    }

    /**
     * 恢复预览
     */
    public synchronized boolean resumePreview() {
        try {
            if (encoder != null && previewSurface != null) {
                encoder.resumePreview(previewSurface);
            }

            LogUtil.d(TAG, "Preview resumed for camera " + cameraId);
            return true;

        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to resume preview for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 获取相机状态
     */
    public CameraConstants.CameraStatus getStatus() {
        return status;
    }

    /**
     * 设置分段完成回调
     */
    public void setSegmentCompletionCallback(SegmentCompletionCallback callback) {
        this.segmentCompletionCallback = callback;

        // 如果编码器已存在，设置回调
        if (encoder != null) {
            encoder.setSegmentCompletionCallback((cameraId, segmentPath) -> {
                if (segmentCompletionCallback != null) {
                    segmentCompletionCallback.onSegmentCompleted(cameraId, segmentPath);
                }
            });
        }
    }
    
    /**
     * 获取相机ID
     */
    public String getCameraId() {
        return cameraId;
    }
    
    /**
     * 获取当前输出路径
     */
    public String getCurrentOutputPath() {
        return currentOutputPath;
    }
    
    /**
     * 检查是否正在录制
     */
    public boolean isRecording() {
        return status == CameraConstants.CameraStatus.RECORDING;
    }
    
    /**
     * 检查是否可用
     */
    public boolean isAvailable() {
        return status != CameraConstants.CameraStatus.ERROR;
    }
    
    /**
     * 释放资源
     */
    public synchronized void release() {
        try {
            if (encoder != null) {
                encoder.stopTest();
                encoder = null;
            }
            
            previewSurface = null;
            currentOutputPath = null;
            status = CameraConstants.CameraStatus.STOPPED;
            
            LogUtil.d(TAG, "CameraController released for camera " + cameraId);
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception releasing CameraController for camera " + cameraId, e);
        }
    }
}
