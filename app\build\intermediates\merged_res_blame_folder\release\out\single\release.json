[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\layout_activity_camera_preview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\layout\\activity_camera_preview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\layout_item_video_record.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\layout\\item_video_record.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\drawable_recording_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\drawable\\recording_status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\drawable_text_stroke_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\drawable\\text_stroke_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\drawable_button_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\drawable\\button_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\drawable_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\drawable\\button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\drawable_settings_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\drawable\\settings_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\drawable_play_pause_button_state.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\drawable\\play_pause_button_state.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\drawable_recording_status_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\drawable\\recording_status_icon.xml"}, {"merged": "com.autolink.sbjk.app-merged_res-37:/layout_activity_main.xml.flat", "source": "com.autolink.sbjk.app-main-38:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-merged_res-37:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-main-38:\\mipmap-hdpi\\ic_launcher_round.webp"}]