package com.autolink.sbjk.storage;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import com.autolink.sbjk.core.storage.StorageCleanupManager;

import java.io.File;
import java.io.IOException;

import static org.junit.Assert.*;

/**
 * StorageCleanupManager单元测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class StorageCleanupManagerTest {
    
    private StorageCleanupManager storageCleanupManager;
    private File testDirectory;
    
    @Before
    public void setUp() throws IOException {
        // 创建临时测试目录
        testDirectory = new File(System.getProperty("java.io.tmpdir"), "test_storage");
        if (!testDirectory.exists()) {
            testDirectory.mkdirs();
        }
        
        storageCleanupManager = new StorageCleanupManager(testDirectory.getAbsolutePath());
    }
    
    @Test
    public void testGetStorageInfo() {
        // 测试获取存储信息
        StorageCleanupManager.StorageInfo info = storageCleanupManager.getStorageInfo();
        
        assertNotNull("存储信息不应为null", info);
        assertTrue("总空间应大于0", info.totalBytes >= 0);
        assertTrue("可用空间应大于等于0", info.availableBytes >= 0);
        assertTrue("已用空间应大于等于0", info.usedBytes >= 0);
    }
    
    @Test
    public void testStorageInfoCalculations() {
        // 测试存储信息计算
        StorageCleanupManager.StorageInfo info = new StorageCleanupManager.StorageInfo(
            1000L, 300L, 700L);
        
        assertEquals("已用百分比计算错误", 0.3, info.getUsedPercent(), 0.01);
        assertEquals("可用百分比计算错误", 0.7, info.getAvailablePercent(), 0.01);
    }
    
    @Test
    public void testCleanupResultCreation() {
        // 测试清理结果创建
        StorageCleanupManager.StorageCleanupResult result = 
            new StorageCleanupManager.StorageCleanupResult(true, "测试成功", 5, 1024L);
        
        assertTrue("清理应成功", result.success);
        assertEquals("消息应匹配", "测试成功", result.message);
        assertEquals("清理文件数应匹配", 5, result.cleanedFileCount);
        assertEquals("清理字节数应匹配", 1024L, result.cleanedBytes);
    }
    
    @Test
    public void testCheckAndCleanupWithSufficientSpace() throws IOException {
        // 创建一些测试文件
        createTestVideoFiles(3);
        
        // 在空间充足的情况下测试（大多数系统都有足够空间）
        StorageCleanupManager.StorageCleanupResult result = storageCleanupManager.checkAndCleanup();
        
        assertNotNull("清理结果不应为null", result);
        // 注意：由于大多数测试环境空间充足，这里主要测试方法不会崩溃
    }
    
    /**
     * 创建测试用的视频文件
     */
    private void createTestVideoFiles(int count) throws IOException {
        for (int i = 0; i < count; i++) {
            File testFile = new File(testDirectory, "test_video_" + i + ".mp4");
            if (!testFile.exists()) {
                testFile.createNewFile();
                // 写入一些测试数据
                java.nio.file.Files.write(testFile.toPath(), 
                    ("Test video content " + i).getBytes());
            }
        }
    }
    
    /**
     * 清理测试文件
     */
    private void cleanupTestFiles() {
        if (testDirectory != null && testDirectory.exists()) {
            File[] files = testDirectory.listFiles();
            if (files != null) {
                for (File file : files) {
                    file.delete();
                }
            }
            testDirectory.delete();
        }
    }
    
    @org.junit.After
    public void tearDown() {
        cleanupTestFiles();
    }
}
