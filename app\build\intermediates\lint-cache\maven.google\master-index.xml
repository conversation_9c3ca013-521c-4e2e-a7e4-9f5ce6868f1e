<?xml version='1.0' encoding='UTF-8'?>
<metadata>
  <android.arch.core/>
  <android.arch.lifecycle/>
  <android.arch.navigation/>
  <android.arch.paging/>
  <android.arch.persistence/>
  <android.arch.persistence.room/>
  <android.arch.work/>
  <androidx.activity/>
  <androidx.ads/>
  <androidx.annotation/>
  <androidx.appcompat/>
  <androidx.appfunctions/>
  <androidx.appsearch/>
  <androidx.arch.core/>
  <androidx.asynclayoutinflater/>
  <androidx.autofill/>
  <androidx.baselineprofile/>
  <androidx.baselineprofile.apptarget/>
  <androidx.baselineprofile.consumer/>
  <androidx.baselineprofile.producer/>
  <androidx.benchmark/>
  <androidx.biometric/>
  <androidx.bluetooth/>
  <androidx.browser/>
  <androidx.camera/>
  <androidx.camera.featurecombinationquery/>
  <androidx.camera.media3/>
  <androidx.camera.viewfinder/>
  <androidx.car/>
  <androidx.car.app/>
  <androidx.cardview/>
  <androidx.collection/>
  <androidx.compose/>
  <androidx.compose.animation/>
  <androidx.compose.compiler/>
  <androidx.compose.foundation/>
  <androidx.compose.material/>
  <androidx.compose.material3/>
  <androidx.compose.material3.adaptive/>
  <androidx.compose.runtime/>
  <androidx.compose.ui/>
  <androidx.concurrent/>
  <androidx.constraintlayout/>
  <androidx.contentpager/>
  <androidx.coordinatorlayout/>
  <androidx.core/>
  <androidx.core.uwb/>
  <androidx.credentials/>
  <androidx.credentials.providerevents/>
  <androidx.credentials.registry/>
  <androidx.cursoradapter/>
  <androidx.customview/>
  <androidx.databinding/>
  <androidx.datastore/>
  <androidx.documentfile/>
  <androidx.draganddrop/>
  <androidx.drawerlayout/>
  <androidx.dynamicanimation/>
  <androidx.emoji/>
  <androidx.emoji2/>
  <androidx.enterprise/>
  <androidx.exifinterface/>
  <androidx.fragment/>
  <androidx.games/>
  <androidx.gaming/>
  <androidx.glance/>
  <androidx.gradle/>
  <androidx.graphics/>
  <androidx.gridlayout/>
  <androidx.health/>
  <androidx.health.connect/>
  <androidx.heifwriter/>
  <androidx.hilt/>
  <androidx.ink/>
  <androidx.input/>
  <androidx.interpolator/>
  <androidx.javascriptengine/>
  <androidx.leanback/>
  <androidx.legacy/>
  <androidx.lifecycle/>
  <androidx.lint/>
  <androidx.loader/>
  <androidx.localbroadcastmanager/>
  <androidx.media/>
  <androidx.media2/>
  <androidx.media3/>
  <androidx.mediarouter/>
  <androidx.metrics/>
  <androidx.multidex/>
  <androidx.navigation/>
  <androidx.navigation.safeargs/>
  <androidx.navigation.safeargs.kotlin/>
  <androidx.navigation3/>
  <androidx.navigationevent/>
  <androidx.paging/>
  <androidx.palette/>
  <androidx.pdf/>
  <androidx.percentlayout/>
  <androidx.performance/>
  <androidx.photopicker/>
  <androidx.preference/>
  <androidx.print/>
  <androidx.privacysandbox.activity/>
  <androidx.privacysandbox.ads/>
  <androidx.privacysandbox.library/>
  <androidx.privacysandbox.plugins/>
  <androidx.privacysandbox.sdkruntime/>
  <androidx.privacysandbox.tools/>
  <androidx.privacysandbox.ui/>
  <androidx.profileinstaller/>
  <androidx.recommendation/>
  <androidx.recyclerview/>
  <androidx.remotecallback/>
  <androidx.resourceinspection/>
  <androidx.room/>
  <androidx.savedstate/>
  <androidx.security/>
  <androidx.sharetarget/>
  <androidx.slice/>
  <androidx.slidingpanelayout/>
  <androidx.sqlite/>
  <androidx.startup/>
  <androidx.swiperefreshlayout/>
  <androidx.test/>
  <androidx.test.espresso/>
  <androidx.test.espresso.idling/>
  <androidx.test.ext/>
  <androidx.test.janktesthelper/>
  <androidx.test.services/>
  <androidx.test.uiautomator/>
  <androidx.textclassifier/>
  <androidx.tracing/>
  <androidx.transition/>
  <androidx.tv/>
  <androidx.tvprovider/>
  <androidx.ui/>
  <androidx.vectordrawable/>
  <androidx.versionedparcelable/>
  <androidx.viewpager/>
  <androidx.viewpager2/>
  <androidx.wear/>
  <androidx.wear.compose/>
  <androidx.wear.protolayout/>
  <androidx.wear.tiles/>
  <androidx.wear.watchface/>
  <androidx.wear.watchfacepush/>
  <androidx.webkit/>
  <androidx.window/>
  <androidx.window.extensions.core/>
  <androidx.work/>
  <androidx.xr.arcore/>
  <androidx.xr.compose/>
  <androidx.xr.compose.material3/>
  <androidx.xr.runtime/>
  <androidx.xr.scenecore/>
  <com.android/>
  <com.android.ai-pack/>
  <com.android.application/>
  <com.android.art/>
  <com.android.asset-pack/>
  <com.android.asset-pack-bundle/>
  <com.android.billingclient/>
  <com.android.car.setupwizardlib/>
  <com.android.car.ui/>
  <com.android.compose.screenshot/>
  <com.android.databinding/>
  <com.android.designcompose/>
  <com.android.dynamic-feature/>
  <com.android.experimental.built-in-kotlin/>
  <com.android.extensions.xr/>
  <com.android.fused-library/>
  <com.android.identity/>
  <com.android.installreferrer/>
  <com.android.internal.settings/>
  <com.android.java.tools.build/>
  <com.android.kotlin.multiplatform.library/>
  <com.android.legacy-kapt/>
  <com.android.library/>
  <com.android.lint/>
  <com.android.ndk.thirdparty/>
  <com.android.privacy-sandbox-sdk/>
  <com.android.reporting/>
  <com.android.security.autorepro/>
  <com.android.security.autorepro.apptest/>
  <com.android.security.autorepro.javahosttest/>
  <com.android.security.autorepro.ndktest/>
  <com.android.security.autorepro.submission/>
  <com.android.security.lint/>
  <com.android.settings/>
  <com.android.support/>
  <com.android.support.constraint/>
  <com.android.support.test/>
  <com.android.support.test.espresso/>
  <com.android.support.test.espresso.idling/>
  <com.android.support.test.janktesthelper/>
  <com.android.support.test.services/>
  <com.android.support.test.uiautomator/>
  <com.android.test/>
  <com.android.tools/>
  <com.android.tools.adblib/>
  <com.android.tools.analytics-library/>
  <com.android.tools.apkdeployer/>
  <com.android.tools.apkparser/>
  <com.android.tools.build/>
  <com.android.tools.build.jetifier/>
  <com.android.tools.chunkio/>
  <com.android.tools.compose/>
  <com.android.tools.ddms/>
  <com.android.tools.emulator/>
  <com.android.tools.external.com-intellij/>
  <com.android.tools.external.org-jetbrains/>
  <com.android.tools.fakeadbserver/>
  <com.android.tools.internal.build.test/>
  <com.android.tools.journeys/>
  <com.android.tools.layoutlib/>
  <com.android.tools.lint/>
  <com.android.tools.metalava/>
  <com.android.tools.pixelprobe/>
  <com.android.tools.screenshot/>
  <com.android.tools.smali/>
  <com.android.tools.utp/>
  <com.android.volley/>
  <com.crashlytics.sdk.android/>
  <com.google.ads.afsn/>
  <com.google.ads.interactivemedia.v3/>
  <com.google.ads.mediation/>
  <com.google.ai.client.generativeai/>
  <com.google.ai.edge.aicore/>
  <com.google.ai.edge.litert/>
  <com.google.ai.edge.localagents/>
  <com.google.ambient.crossdevice/>
  <com.google.android.ads/>
  <com.google.android.ads.consent/>
  <com.google.android.appcrawler/>
  <com.google.android.apps.common.testing.accessibility.framework/>
  <com.google.android.car.connectionservice/>
  <com.google.android.datatransport/>
  <com.google.android.engage/>
  <com.google.android.enterprise.connectedapps/>
  <com.google.android.exoplayer/>
  <com.google.android.fhir/>
  <com.google.android.flexbox/>
  <com.google.android.games/>
  <com.google.android.gms/>
  <com.google.android.gms.strict-version-matcher-plugin/>
  <com.google.android.instantapps/>
  <com.google.android.instantapps.thirdpartycompat/>
  <com.google.android.libraries.ads.mobile.sdk/>
  <com.google.android.libraries.car/>
  <com.google.android.libraries.cloud.telco.subgraph/>
  <com.google.android.libraries.enterprise.amapi/>
  <com.google.android.libraries.healthdata/>
  <com.google.android.libraries.identity.googleid/>
  <com.google.android.libraries.maps/>
  <com.google.android.libraries.mapsplatform.secrets-gradle-plugin/>
  <com.google.android.libraries.mapsplatform.transportation/>
  <com.google.android.libraries.navigation/>
  <com.google.android.libraries.places/>
  <com.google.android.libraries.play.games/>
  <com.google.android.libraries.sdkcoroutines/>
  <com.google.android.libraries.searchinapps/>
  <com.google.android.livesharing/>
  <com.google.android.material/>
  <com.google.android.mdoc/>
  <com.google.android.mediahome/>
  <com.google.android.meet/>
  <com.google.android.odml/>
  <com.google.android.play/>
  <com.google.android.recaptcha/>
  <com.google.android.support/>
  <com.google.android.things/>
  <com.google.android.tv/>
  <com.google.android.tv.tvpanelframework/>
  <com.google.android.ump/>
  <com.google.android.wearable/>
  <com.google.android.wearable.watchface.validator/>
  <com.google.androidbrowserhelper/>
  <com.google.ar/>
  <com.google.ar.sceneform/>
  <com.google.ar.sceneform.ux/>
  <com.google.assistant.appactions/>
  <com.google.assistant.suggestion/>
  <com.google.camerax.effects/>
  <com.google.chromeos/>
  <com.google.cose/>
  <com.google.d2c/>
  <com.google.devtools.ksp/>
  <com.google.fhir/>
  <com.google.firebase/>
  <com.google.firebase.appdistribution/>
  <com.google.firebase.crashlytics/>
  <com.google.firebase.firebase-perf/>
  <com.google.firebase.testlab/>
  <com.google.gms/>
  <com.google.gms.google-services/>
  <com.google.jacquard/>
  <com.google.mediapipe/>
  <com.google.mlkit/>
  <com.google.net.cronet/>
  <com.google.oboe/>
  <com.google.play.policy.insights/>
  <com.google.prefab/>
  <com.google.relay/>
  <com.google.test.platform/>
  <com.google.testing.platform/>
  <io.fabric.sdk.android/>
  <org.chromium.net/>
  <org.jetbrains.kotlin/>
  <org.multipaz/>
  <tools.base.build-system.debug/>
  <zipflinger/>
</metadata>
